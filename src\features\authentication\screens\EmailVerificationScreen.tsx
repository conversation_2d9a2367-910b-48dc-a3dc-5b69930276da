"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, SafeAreaView, TextInput, Animated, Alert, AppState, ImageBackground } from "react-native"
import ReactNativeHapticFeedback from "react-native-haptic-feedback"
import GlassyBox from "../../../shared/components/ui/GlassyBox"
import Clipboard from '@react-native-clipboard/clipboard'
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../../navigation/navigation"
import { useTheme } from "../../../shared/components/layout/ThemeContext"
import ApiService from "../../../infrastructure/api/apiService"
import Svg, { Path } from "react-native-svg"
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import { navigationHandler } from '../../../navigation/handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, "EmailVerification">

const EmailVerificationScreen = ({ route }: Props) => {
  const { theme, isDark } = useTheme()
  const [code, setCode] = useState(["", "", "", "", "", ""])
  const [timer, setTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [isBackButtonPressed, setIsBackButtonPressed] = useState(false)
  const [isResendPressed, setIsResendPressed] = useState(false)
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [showToast, setShowToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')
  const bounceAnim = useRef(new Animated.Value(1)).current
  const toastAnim = useRef(new Animated.Value(300)).current
  const inputRefs = useRef<TextInput[]>([])
  const lastClipboardContent = useRef<string>('')

  // Get email from route params (passed from EmailInputScreen)
  const email = route.params?.email || "<EMAIL>"

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true)
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  // Temporarily disabled clipboard monitoring to fix stack overflow
  // TODO: Re-implement clipboard monitoring with proper dependency management

  const handleCodeChange = (text: string, index: number) => {
    // Prevent changes while verification is in progress
    if (isLoading) {
      return;
    }

    // Only allow numeric characters
    if (!/^\d*$/.test(text)) {
      return;
    }

    // Reset verification status when user starts typing again
    if (verificationStatus !== 'idle') {
      setVerificationStatus('idle')
    }

    const newCode = [...code]
    newCode[index] = text

    setCode(newCode)

    // Trigger haptic feedback on input
    if (text) {
      ReactNativeHapticFeedback.trigger("impactMedium", {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: true,
      })
    }

    // Auto-focus next input
    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }

    // Auto-submit when code is complete
    if (text && index === 5) {
      const completeCode = newCode.join("")
      if (completeCode.length === 6) {
        // Small delay to show the last digit before submitting
        setTimeout(() => {
          handleVerify(completeCode)
        }, 300)
      }
    }
  }

  const handleKeyPress = (key: string, index: number) => {
    if (key === "Backspace" && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  // Temporarily removed debug methods to fix stack overflow

  const handleVerify = async (providedCode?: string) => {
    const verificationCode = providedCode || code.join("")
    if (verificationCode.length === 6 && !isLoading) {
      setIsLoading(true)

      try {
        logger.info('Starting email OTP verification', {
          email,
          codeLength: verificationCode.length,
          code: '***REDACTED***'
        }, 'auth');
        const response = await ApiService.verifyEmailOTP(email, verificationCode)

        logger.info('Email OTP verification response received', {
          hasResponse: !!response,
          hasData: !!response?.data,
          status: response?.status || response?.data?.status,
          responseKeys: response ? Object.keys(response) : 'no response'
        }, 'auth');

        // Check if verification was successful
        const isSuccess = response?.data?.status === 'success' || response?.status === 200;

        if (isSuccess && response?.data) {
          setVerificationStatus('success')
          logger.info('Email verification successful - navigating to next step', {
            hasData: !!response.data,
            backendStatus: response.data.status,
            httpStatus: response.status,
            hasDataProperty: !!response.data.data,
            hasTokens: !!(response.data.data?.tokens || response.data.tokens),
            responseStructure: Object.keys(response.data)
          }, 'auth');

          // Check for tokens in the correct location (API service already stores them)
          const tokens = response.data.data?.tokens || response.data.tokens;
          if (tokens) {
            try {
              // API service already stored tokens, just verify they're accessible
              const storedTokens = await ApiService.forceRefreshTokensFromStorage();
              if (!storedTokens.accessToken) {
                throw new Error('Token storage verification failed');
              }
              logger.info('Token storage verified successfully', {
                accessTokenPreview: storedTokens.accessToken.substring(0, 20) + '...',
                hasRefreshToken: !!storedTokens.refreshToken
              }, 'auth');

            } catch (tokenError) {
              logger.error('Error verifying token storage after email verification', tokenError, 'auth');
              // Don't proceed with navigation if token verification fails
              setVerificationStatus('error');
              setToastMessage('Authentication setup failed. Please try again.');
              setShowToast(true);
              return;
            }
          } else {
            logger.error('No tokens received in email verification response', {
              responseData: response.data,
              hasDataProperty: !!response.data.data,
              dataKeys: response.data.data ? Object.keys(response.data.data) : 'no data property'
            }, 'auth');
            setVerificationStatus('error');
            setToastMessage('Authentication setup failed. Please try again.');
            setShowToast(true);
            return;
          }

          // Navigate after ensuring tokens are stored and verified
          setTimeout(async () => {
            try {
              logger.info('Starting navigation after email verification', { email }, 'auth');
              // Use navigation handler to route based on user setup status
              await navigationHandler.navigateAfterEmailVerification(email)
              logger.info('Navigation completed successfully after email verification', null, 'auth');
            } catch (error) {
              logger.error('Error navigating after email verification', error, 'auth');
              // Fallback to showing success state if navigation fails
              navigationHandler.handleEmailVerificationSuccess()
            }
          }, 1000) // Longer delay to ensure token storage and verification completes
          
        } else {
          // Log the actual response for debugging
          logger.error('Email verification failed or invalid response', {
            hasResponse: !!response,
            hasData: !!response?.data,
            backendStatus: response?.data?.status,
            httpStatus: response?.status,
            message: response?.data?.message,
            fullResponse: response
          }, 'auth');
          throw new Error(response?.data?.message || 'Email verification failed')
        }
      } catch (error) {
        logger.error('Email verification failed', error, 'auth');
        setVerificationStatus('error')
        let errorMessage = 'Please check your code and try again';
        if (error instanceof Error && error.message) {
          if (error.message.toLowerCase().includes('expired')) {
            errorMessage = 'This verification code has expired. Please request a new one.';
          } else if (error.message.toLowerCase().includes('invalid')) {
            errorMessage = 'The verification code is invalid. Please try again.';
          }
        }
        setToastMessage(errorMessage)
        setShowToast(true)
        Animated.spring(toastAnim, {
          toValue: 0,
          useNativeDriver: true,
          friction: 4,
        }).start()
        // Hide toast after 3 seconds
        setTimeout(() => {
          Animated.timing(toastAnim, {
            toValue: 300,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            setShowToast(false)
            setVerificationStatus('idle')
            setCode(["", "", "", "", "", ""])
            inputRefs.current[0]?.focus()
          })
        }, 3000)
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleResend = async () => {
    if (canResend && !isResending) {
      setIsResending(true)

      try {
        logger.info('Resending OTP to email', { email }, 'auth');
        const response = await ApiService.sendEmailOTP(email, 'verification')

        if (response.status === 'success') {
          logger.info('OTP resent successfully', null, 'auth');
          setTimer(60)
          setCanResend(false)
          setCode(["", "", "", "", "", ""])
          inputRefs.current[0]?.focus()
          Alert.alert('Success', 'Verification code sent successfully')
        } else {
          logger.error('Failed to resend OTP', { message: response.message }, 'auth');
          Alert.alert('Error', response.message || 'Failed to resend code')
        }
      } catch (error) {
        logger.error('Error resending OTP', error, 'auth');
        const errorMessage = error instanceof Error ? error.message : 'Failed to resend code. Please try again.'
        Alert.alert('Error', errorMessage)
      } finally {
        setIsResending(false)
      }
    }
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    backButton: {
      width: 44,
      height: 44,
      justifyContent: 'center',
      alignItems: 'center',
    },
    backButtonPressed: {
      transform: [{ scale: 0.95 }],
    },
    content: {
      paddingHorizontal: 24,
      paddingTop: 20,
      alignItems: "center",
    },
    title: {
      fontSize: 24,
      fontWeight: "600",
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: "center",
    },
    subtitle: {
      fontSize: 15,
      color: theme.colors.muted,
      lineHeight: 22,
      marginBottom: 8,
      textAlign: "center",
      maxWidth: 280,
    },
    emailAddress: {
      fontSize: 15,
      color: theme.colors.text,
      fontWeight: "600",
      marginBottom: 40,
      textAlign: "center",
    },
    codeContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
      maxWidth: 300,
      marginBottom: 32,
    },
    codeInput: {
      width: 40,
      height: 50,
      textAlign: "center",
      fontSize: 18,
      fontWeight: "600",
      color: theme.colors.text,
      borderBottomWidth: 2,
      borderBottomColor: isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
    },
    codeInputFilled: {
      borderBottomColor: '#8000FF', // Neon purple
      borderBottomWidth: 3, // Make the line slightly thicker when active
      // Removed all shadow properties to eliminate box glow
    },
    codeInputSuccess: {
      borderBottomColor: '#10B981', // Green color for success
      borderBottomWidth: 3,
      // Removed all shadow properties to eliminate box glow
    },
    codeInputError: {
      borderBottomColor: '#EF4444', // Red color for error
      borderBottomWidth: 3,
      // Removed all shadow properties to eliminate box glow
    },
    resendContainer: {
      alignItems: "center",
      marginBottom: 40,
    },
    resendButton: {
      padding: 12,
    },
    resendButtonText: {
      fontSize: 14,
      color: canResend ? theme.colors.primary : theme.colors.muted,
      fontWeight: "500",
      textAlign: "center",
    },
    glassyResendButton: {
      borderRadius: 20,
      padding: 2, // Small padding to ensure the glass effect surrounds the button
    },
  })

  return (
    <View style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor="transparent" translucent={true} />
      <ImageBackground
        source={isDark ? require("../../../../assets/images/bg.jpeg") : require("../../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      {/* Header */}
      {/* Header */}
      <SafeAreaView style={styles.header}>
        <TouchableOpacity 
          style={[
            styles.backButton,
            isBackButtonPressed && styles.backButtonPressed
          ]}
          onPress={() => navigationHandler.navigateBackFromEmailVerification()}
          onPressIn={() => setIsBackButtonPressed(true)}
          onPressOut={() => setIsBackButtonPressed(false)}
          activeOpacity={0.8}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Animated.View 
            style={{ 
              transform: [
                { translateX: isBackButtonPressed ? -1 : 0 }
              ]
            }}
          >
            <BackArrowIcon 
              size={18} 
              color={theme.colors.text} 
            />
          </Animated.View>
        </TouchableOpacity>
      </SafeAreaView>

      {/* Content */}
      <SafeAreaView style={styles.content}>
        <Text style={styles.title}>Enter verification code</Text>
        <Text style={styles.subtitle}>
          We sent a 6-digit code to
        </Text>
        <Text style={styles.emailAddress}>{email}</Text>

        {/* Code Input */}
        <View style={styles.codeContainer}>
          {code.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                if (ref) inputRefs.current[index] = ref
              }}
              style={[
                styles.codeInput,
                digit && verificationStatus === 'idle' && styles.codeInputFilled,
                verificationStatus === 'success' && styles.codeInputSuccess,
                verificationStatus === 'error' && styles.codeInputError
              ]}
              value={digit}
              onChangeText={(text) => handleCodeChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={1}
              autoFocus={index === 0}
              editable={!isLoading}
            />
          ))}
        </View>

        {/* Resend */}
        <View style={styles.resendContainer}>
          <GlassyBox style={styles.glassyResendButton}>
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResend}
              disabled={!canResend}
              activeOpacity={0.7}
              onPressIn={() => {
                setIsResendPressed(true);
                Animated.spring(bounceAnim, {
                  toValue: 0.85,
                  useNativeDriver: true,
                  friction: 3,
                }).start();
              }}
              onPressOut={() => {
                setIsResendPressed(false);
                Animated.spring(bounceAnim, {
                  toValue: 1,
                  useNativeDriver: true,
                  friction: 3,
                }).start();
              }}
            >
              <Animated.View
                style={{
                  transform: [{ scale: bounceAnim }]
                }}
              >
                <Text style={[styles.resendButtonText, { color: canResend ? '#8000FF' : theme.colors.muted }]}>
                  {isResending ? "Sending..." : canResend ? "Resend" : `Resend in ${timer}s`}
                </Text>
              </Animated.View>
            </TouchableOpacity>
          </GlassyBox>
        </View>

        {/* Toast Notification */}
        {showToast && (
          <Animated.View
            style={{
              position: 'absolute',
              top: 50,
              right: 0,
              backgroundColor: '#EF4444', // Red color for error
              paddingVertical: 10,
              paddingHorizontal: 20,
              borderRadius: 8,
              marginHorizontal: 20,
              alignItems: 'center',
              justifyContent: 'center',
              transform: [{ translateX: toastAnim }],
              zIndex: 1000,
            }}
          >
            <Text style={{ color: '#FFFFFF', fontSize: 14, fontWeight: '500' }}>
              {toastMessage}
            </Text>
          </Animated.View>
        )}
      </SafeAreaView>
    </View>
  )
}

const BackArrowIcon = ({ color, size }: { color: string, size: number }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 11.5H9.41L14.46 6.46C14.85 6.07 14.85 5.44 14.46 5.05C14.27 4.86 14.02 4.76 13.76 4.76C13.5 4.76 13.25 4.86 13.05 5.05L6.46 11.64C6.07 12.03 6.07 12.66 6.46 13.05L13.05 19.64C13.44 20.03 14.07 20.03 14.46 19.64C14.85 19.25 14.85 18.62 14.46 18.23L9.41 13.18H20C20.55 13.18 21 12.73 21 12.18C21 11.63 20.55 11.18 20 11.18V11.5Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
)

export default EmailVerificationScreen
