import { apiService } from '../../../infrastructure/api/apiService';
import { logger } from '../../../infrastructure/monitoring/productionLogger';
import { userDeletionHandler } from './userDeletionHandler';

export type SetupStep = 'NameSetup' | 'PinSetup' | 'PinVerification' | 'BiometricSetup' | 'SetupComplete';

export interface SetupStatus {
  hasPinSetup: boolean;
  hasBiometricSetup: boolean;
  hasProfileSetup: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  setupComplete: boolean;
}

export interface UserData {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  picture?: string;
}

export interface SetupStatusResponse {
  setupStatus: SetupStatus;
  user: UserData;
}

export interface PinSetupRequest {
  pin: string;
  confirmPin: string;
}

export interface PinVerifyRequest {
  pin: string;
}

export interface PinVerifyResponse {
  pinVerified: boolean;
  setupStatus: SetupStatus;
  user: UserData & { balance?: string };
  welcomeMessage: string;
}

export interface BiometricSetupRequest {
  enabled: boolean;
  biometricType?: 'fingerprint' | 'face' | 'voice' | 'iris';
  deviceInfo?: {
    platform: string;
    version: string | number;
    model: string;
  };
}

export interface ProfileSetupRequest {
  firstName: string;
  lastName?: string;
  dateOfBirth?: string;
  avatar?: string;
}

class SetupService {
  /**
   * Debug method to check if tokens are stored
   */
  async checkTokenStorage(): Promise<any> {
    try {
      logger.info('🔍 Checking token storage status', null, 'setup');

      const accessToken = await apiService.getAccessToken();
      const refreshToken = await apiService.getRefreshToken();

      // Also check fresh tokens from keychain
      const freshTokens = await apiService.forceRefreshTokensFromStorage();

      const result = {
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
        accessTokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'No token',
        refreshTokenPreview: refreshToken ? refreshToken.substring(0, 20) + '...' : 'No token',
        freshAccessTokenPreview: freshTokens.accessToken ? freshTokens.accessToken.substring(0, 20) + '...' : 'No fresh token',
        tokensMatch: accessToken === freshTokens.accessToken,
        timestamp: new Date().toISOString()
      };

      logger.info('Token storage check result:', result, 'setup');
      return result;
    } catch (error: any) {
      logger.error('Token storage check failed:', error, 'setup');
      throw error;
    }
  }

  /**
   * Test user profile endpoint
   */
  async testUserProfile(): Promise<any> {
    try {
      logger.info('🧪 Testing user profile endpoint', null, 'setup');
      
      // Check tokens first
      const accessToken = await apiService.getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available for testing');
      }
      
      // Try to get user profile
      const response = await apiService.get('/user/profile');
      
      logger.info('✅ User profile test successful', { 
        hasUser: !!response.data?.data?.user,
        userId: response.data?.data?.user?.id 
      }, 'setup');
      
      return {
        success: true,
        message: 'User profile endpoint working',
        user: response.data?.data?.user,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      logger.error('❌ User profile test failed', { 
        message: error.message, 
        status: error.status 
      }, 'setup');
      
      return {
        success: false,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test setup status endpoint to debug setup flow issues
   */
  async testSetupStatus(): Promise<any> {
    try {
      logger.info('🧪 Testing setup status endpoint', null, 'setup');
      
      // Check tokens first
      const accessToken = await apiService.getAccessToken();
      logger.info('Token status for setup test', { 
        hasToken: !!accessToken,
        tokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'No token'
      }, 'setup');
      
      // Try to get setup status
      const setupResponse = await this.getSetupStatus();
      
      logger.info('✅ Setup status test successful', setupResponse, 'setup');
      
      return {
        success: true,
        message: 'Setup status endpoint working',
        setupStatus: setupResponse.setupStatus,
        user: setupResponse.user,
        shouldShowSetup: !setupResponse.setupStatus.setupComplete,
        nextStep: await this.getNextSetupStep(),
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      logger.error('❌ Setup status test failed', { 
        message: error.message, 
        status: error.status 
      }, 'setup');
      
      return {
        success: false,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test endpoint to verify setup routes work without auth
   */
  async testSetupEndpoint(): Promise<any> {
    try {
      logger.info('🧪 Testing setup endpoint without auth', null, 'setup');
      
      const response = await apiService.get('/setup/test', {}, {
        skipAuth: true, // Skip authentication for test
        timeout: 10000,
        retries: 1,
      });
      
      logger.info('✅ Setup test endpoint response:', response.data, 'setup');
      return response.data;
    } catch (error: any) {
      logger.error('❌ Setup test endpoint failed:', error, 'setup');
      throw error;
    }
  }

  /**
   * Get current setup status for the user
   */
  async getSetupStatus(): Promise<SetupStatusResponse> {
    try {
      logger.info('🔍 Getting setup status for authenticated user', null, 'setup');
      
      // Check if user is authenticated first
      const accessToken = await apiService.getAccessToken();
      const useAuth = !!accessToken;
      
      logger.info('Setup status request config', { 
        hasToken: useAuth,
        willUseAuth: useAuth 
      }, 'setup');
      
      // Use authentication if available, otherwise skip for public access
      // CRITICAL: No caching - always get fresh data from backend
      const response = await apiService.get('/setup/status', {}, {
        timeout: 8000, // Reduced timeout for faster response (8 seconds)
        retries: 1, // Single retry for faster failure detection
        cache: false, // NO CACHING - always fresh from backend
        skipAuth: !useAuth, // Use auth if token is available
      });
      
      logger.info('Setup status API response received', {
        hasData: !!response.data,
        status: response.data?.status,
        hasSetupStatus: !!response.data.data?.setupStatus,
        hasUser: !!response.data.data?.user
      }, 'setup');
      
      if (response.data.status === 'success') {
        const setupData = response.data.data;
        
        // Log detailed setup status for debugging
        logger.info('📊 Setup status details', {
          setupComplete: setupData.setupStatus?.setupComplete,
          hasPinSetup: setupData.setupStatus?.hasPinSetup,
          hasProfileSetup: setupData.setupStatus?.hasProfileSetup,
          hasBiometricSetup: setupData.setupStatus?.hasBiometricSetup,
          userFirstName: setupData.user?.firstName,
          userId: setupData.user?.id
        }, 'setup');
        
        return setupData;
      } else {
        // Check if user was deleted
        if (response.data.data?.userDeleted || response.data.data?.redirectToSignup) {
          logger.security('USER_DELETED_DETECTED', { message: response.data.message });
          const error = new Error(response.data.message || 'User account no longer exists');
          (error as any).userDeleted = true;
          throw error;
        }
        
        throw new Error(response.data.message || 'Failed to get setup status');
      }
    } catch (error: any) {
      logger.error('Get setup status error', error, 'setup');
      
      // Provide more specific error messages for better debugging
      if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Setup status request timed out. Please check your connection and try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error while checking setup status. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to get setup status');
      }
    }
  }

  /**
   * Set up user PIN (for new users)
   */
  async setupPin(pinData: PinSetupRequest): Promise<void> {
    try {
      const response = await apiService.post('/setup/pin', pinData);
      
      if (response.data.status !== 'success') {
        throw new Error(response.data.message || 'Failed to set up PIN');
      }

      // Store tokens if they exist in the response (for authentication persistence)
      if (response.data.data?.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        await apiService.storeTokens(accessToken, refreshToken);
        logger.info('Tokens stored after PIN setup', null, 'setup');
      }
    } catch (error: any) {
      logger.error('PIN setup error', error, 'setup');
      
      // Check if PIN is already set and redirect to verification
      if (error.response?.data?.data?.pinAlreadySet) {
        throw new Error('PIN_ALREADY_SET');
      }
      
      throw new Error(error.response?.data?.message || 'Failed to set up PIN');
    }
  }

  /**
   * Verify existing PIN (for returning users)
   */
  async verifyPin(pinData: PinVerifyRequest): Promise<PinVerifyResponse> {
    try {
      logger.info('Starting PIN verification with extended timeout', null, 'setup');
      // Use extended timeout for PIN verification as it may take longer
      const response = await apiService.post('/setup/verify-pin', pinData, { 
        timeout: 45000, // 45 seconds timeout
        retries: 2 // Reduce retries to avoid long wait times
      });
      
      if (response.data.status === 'success') {
        logger.info('PIN verification successful', null, 'setup');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to verify PIN');
      }
    } catch (error: any) {
      logger.error('PIN verification error', error, 'setup');
      
      // Provide more specific error messages
      if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to verify PIN');
      }
    }
  }

  /**
   * Set up biometric authentication
   */
  async setupBiometric(biometricData: BiometricSetupRequest): Promise<void> {
    try {
      const response = await apiService.post('/setup/biometric', biometricData);
      
      if (response.data.status !== 'success') {
        throw new Error(response.data.message || 'Failed to set up biometric authentication');
      }

      // Store tokens if they exist in the response (for authentication persistence)
      if (response.data.data?.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        await apiService.storeTokens(accessToken, refreshToken);
        logger.info('Tokens stored after biometric setup', null, 'setup');
      }
    } catch (error: any) {
      logger.error('Biometric setup error', error, 'setup');
      throw new Error(error.response?.data?.message || 'Failed to set up biometric authentication');
    }
  }

  /**
   * Complete profile setup
   */
  async setupProfile(profileData: ProfileSetupRequest): Promise<UserData> {
    try {
      logger.info('Setting up user profile', { firstName: profileData.firstName }, 'setup');
      
      // Check if we have an access token before making the request
      const accessToken = await apiService.getAccessToken();
      logger.info('Access token status for profile setup', { 
        hasToken: !!accessToken, 
        tokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'No token' 
      }, 'setup');
      
      if (!accessToken) {
        throw new Error('No authentication token available. Please sign in again.');
      }
      
      const response = await apiService.post('/setup/profile', profileData);
      
      if (response.data.status === 'success') {
        logger.info('Profile setup completed successfully', null, 'setup');
        return response.data.data.user;
      } else {
        throw new Error(response.data.message || 'Failed to set up profile');
      }
    } catch (error: any) {
      logger.error('Profile setup error', error, 'setup');
      
      // Handle authentication errors specifically
      if (error.status === 401 || error.message?.includes('Session expired') || error.message?.includes('token') || error.message?.includes('authentication')) {
        const authError = new Error('Your session has expired. Please sign in again to continue setup.');
        (authError as any).isAuthError = true;
        (authError as any).status = 401;
        throw authError;
      }
      
      // Handle other errors
      const errorMessage = error.response?.data?.message || error.message || 'Failed to set up profile';
      throw new Error(errorMessage);
    }
  }

  /**
   * Mark setup as complete
   */
  async completeSetup(): Promise<void> {
    try {
      const response = await apiService.post('/setup/complete');
      
      if (response.data.status !== 'success') {
        throw new Error(response.data.message || 'Failed to complete setup');
      }
    } catch (error: any) {
      logger.error('Complete setup error', error, 'setup');
      throw new Error(error.response?.data?.message || 'Failed to complete setup');
    }
  }

  /**
   * Check if user needs to go through setup flow
   */
  async shouldShowSetup(): Promise<boolean> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      return !setupStatus.setupComplete;
    } catch (error) {
      logger.error('Error checking setup status', error, 'setup');
      // If we can't check, assume setup is needed
      return true;
    }
  }

  /**
   * Get next setup step based on current status
   */
  async getNextSetupStep(): Promise<SetupStep | null> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      
      if (setupStatus.setupComplete) {
        return null; // No setup needed
      }
      
      if (!setupStatus.hasProfileSetup) {
        return 'NameSetup';
      }
      
      if (!setupStatus.hasPinSetup) {
        return 'PinSetup'; // Create new PIN
      }
      
      // If PIN is set but setup is not complete, verify PIN first
      if (setupStatus.hasPinSetup && !setupStatus.setupComplete) {
        return 'PinVerification'; // Verify existing PIN
      }
      
      if (!setupStatus.hasBiometricSetup) {
        return 'BiometricSetup';
      }
      
      return 'SetupComplete';
    } catch (error) {
      logger.error('Error getting next setup step', error, 'setup');
      return 'NameSetup'; // Default to first step
    }
  }

  /**
   * Determine if user should see PIN setup or PIN verification
   */
  async getPinFlowType(): Promise<'setup' | 'verify' | 'complete'> {
    try {
      logger.info('🔍 [PIN-FLOW] Getting setup status to determine PIN flow', null, 'setup');

      const { setupStatus } = await this.getSetupStatus();

      logger.info('🔍 [PIN-FLOW] Setup status retrieved', {
        setupComplete: setupStatus.setupComplete,
        hasPinSetup: setupStatus.hasPinSetup,
        hasProfileSetup: setupStatus.hasProfileSetup,
        isEmailVerified: setupStatus.isEmailVerified
      }, 'setup');

      if (setupStatus.setupComplete) {
        logger.info('✅ [PIN-FLOW] Setup complete - returning "complete"', null, 'setup');
        return 'complete'; // No PIN flow needed
      }

      if (setupStatus.hasPinSetup) {
        logger.info('🔐 [PIN-FLOW] PIN exists - returning "verify"', null, 'setup');
        return 'verify'; // PIN exists, show verification
      } else {
        logger.info('🆕 [PIN-FLOW] No PIN setup - returning "setup"', null, 'setup');
        return 'setup'; // No PIN, show setup
      }
    } catch (error) {
      logger.error('❌ [PIN-FLOW] Error determining PIN flow type', error, 'setup');
      logger.info('🔄 [PIN-FLOW] Defaulting to "setup" due to error', null, 'setup');
      return 'setup'; // Default to setup
    }
  }
}

export const setupService = new SetupService();