import { NavigationProp, NavigationContainerRef } from '@react-navigation/native';
import { RootStackParamList } from '../navigation';
import { logger } from '../../infrastructure/monitoring/productionLogger';

/**
 * Centralized Navigation Handler
 * This is the ONLY file that should handle navigation throughout the entire app
 * All screens should call methods from this handler instead of navigating directly
 */
class NavigationHandler {
  private navigation: NavigationProp<RootStackParamList> | NavigationContainerRef<RootStackParamList> | null = null;

  /**
   * Initialize the navigation handler with navigation object
   * This should be called from the main navigation container
   */
  setNavigation(navigation: NavigationProp<RootStackParamList> | NavigationContainerRef<RootStackParamList>) {
    this.navigation = navigation;
    logger.info('Navigation handler initialized', null, 'navigation');
  }

  /**
   * Ensure navigation is available before performing any navigation action
   */
  private ensureNavigation(): NavigationProp<RootStackParamList> | NavigationContainerRef<RootStackParamList> {
    if (!this.navigation) {
      throw new Error('Navigation not initialized. Call setNavigation() first.');
    }
    return this.navigation;
  }

  // ==================== SPLASH SCREEN NAVIGATION ====================
  
  /**
   * Navigate from splash screen after authentication check
   */
  navigateFromSplash(destination: 'Startup' | 'NameSetup' | 'PinSetup' | 'PinVerification', userData?: any) {
    this.ensureNavigation();
    logger.userAction('SPLASH_NAVIGATION', { destination, hasUserData: !!userData });

    switch (destination) {
      case 'Startup':
        this.resetToScreen('Startup');
        break;
      case 'NameSetup':
        this.resetToScreen('NameSetup', { userData });
        break;
      case 'PinSetup':
        this.resetToScreen('PinSetup', { userData });
        break;
      case 'PinVerification':
        this.resetToScreen('PinVerification', { user: userData });
        break;
    }
  }

  // ==================== STARTUP SCREEN NAVIGATION ====================

  /**
   * Navigate to phone input from startup
   */
  navigateToPhoneInput() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_PHONE_INPUT');
    nav.navigate('PhoneInput');
  }

  /**
   * Navigate to email input from startup
   */
  navigateToEmailInput() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_EMAIL_INPUT');
    nav.navigate('EmailInput');
  }

  /**
   * Navigate to splash after Google sign-in
   */
  navigateToSplashAfterGoogleSignIn() {
    this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SPLASH_AFTER_GOOGLE');
    this.resetToScreen('Splash');
  }

  // ==================== EMAIL FLOW NAVIGATION ====================
  
  /**
   * Navigate from email input to email verification
   */
  navigateToEmailVerification(email: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_EMAIL_VERIFICATION', { emailDomain: email.split('@')[1] || 'unknown' });
    nav.navigate('EmailVerification', { email });
  }

  /**
   * Handle successful email verification - just show success, no further navigation
   */
  handleEmailVerificationSuccess() {
    logger.userAction('EMAIL_VERIFICATION_SUCCESS');
    // Email verification is complete - no navigation needed
    // The screen will handle showing success state
  }

  /**
   * Navigate after successful email verification based on user setup status
   */
  async navigateAfterEmailVerification(email: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_AFTER_EMAIL_VERIFICATION', { emailDomain: email.split('@')[1] || 'unknown' });

    try {
      logger.info('Starting email verification navigation process', { email }, 'navigation');

      // Import setup service dynamically to avoid circular dependencies
      const { setupService } = await import('../../features/authentication/services/setupService');

      // Wait a moment for token storage to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      logger.info('Attempting to get setup status after email verification', null, 'navigation');

      // Get fresh setup status from backend - try with auth first, fallback to no auth
      let setupResponse;
      try {
        setupResponse = await setupService.getSetupStatus(); // Use auth since user is verified
        logger.info('Setup status retrieved successfully', {
          hasSetupStatus: !!setupResponse.setupStatus,
          hasUser: !!setupResponse.user
        }, 'navigation');
      } catch (authError) {
        logger.warn('Auth failed for setup status, attempting token refresh', authError, 'navigation');

        // Try to refresh tokens from storage in case of cache issues
        try {
          const { setupService: refreshedSetupService } = await import('../../features/authentication/services/setupService');
          const refreshedTokens = await refreshedSetupService.checkTokenStorage();
          logger.info('Token refresh attempt result', refreshedTokens, 'navigation');

          // Try setup status again with refreshed tokens
          setupResponse = await refreshedSetupService.getSetupStatus();
          logger.info('Setup status retrieved successfully after token refresh', null, 'navigation');
        } catch (refreshError) {
          logger.warn('Token refresh failed, falling back to default setup status', refreshError, 'navigation');
          // If auth fails completely, assume user needs full setup
          setupResponse = {
            setupStatus: {
              hasProfileSetup: false,
              hasPinSetup: false,
              hasBiometricSetup: false,
              isEmailVerified: true,
              isPhoneVerified: false,
              setupComplete: false,
            },
            user: {
              email: email,
              isEmailVerified: true,
              isPhoneVerified: false,
            },
          };
        }
      }

      if (!setupResponse.user) {
        logger.error('No user data found after email verification', null, 'navigation');
        this.resetToScreen('Startup');
        return;
      }

      // Create comprehensive user data object
      const userData = {
        ...setupResponse.user,
        email: email,
        isEmailVerified: true,
        authMethod: 'email',
        isNewUser: !setupResponse.setupStatus.hasProfileSetup,
        hasPinSetup: setupResponse.setupStatus.hasPinSetup,
        hasBiometricSetup: setupResponse.setupStatus.hasBiometricSetup,
        hasProfileSetup: setupResponse.setupStatus.hasProfileSetup,
        setupComplete: setupResponse.setupStatus.setupComplete,
      };

      logger.info('Email verification navigation - user setup status', {
        hasProfileSetup: setupResponse.setupStatus.hasProfileSetup,
        hasPinSetup: setupResponse.setupStatus.hasPinSetup,
        hasBiometricSetup: setupResponse.setupStatus.hasBiometricSetup,
        setupComplete: setupResponse.setupStatus.setupComplete,
        hasFirstName: !!(userData as any).firstName
      }, 'navigation');

      // Route based on setup completion status - follow the exact sequence
      if (!setupResponse.setupStatus.hasProfileSetup || !(userData as any).firstName || (userData as any).firstName.trim() === '') {
        // Step 1: User needs to set up their name first
        logger.info('Routing to name setup after email verification', null, 'navigation');
        nav.navigate('NameSetup', { userData });
      } else if (!setupResponse.setupStatus.hasPinSetup) {
        // Step 2: User has name but no PIN - go to PIN setup to create PIN
        logger.info('Routing to PIN setup after email verification', null, 'navigation');
        nav.navigate('PinSetup', { userData });
      } else {
        // Step 4: User has name, PIN, and biometric - go to PIN verification before main app
        // OR setup is complete - either way, PIN verification is the final step before main app
        logger.info('Routing to PIN verification after email verification', null, 'navigation');
        nav.navigate('PinVerification', { user: userData });
      }

    } catch (error) {
      logger.error('Error during email verification navigation', error, 'navigation');
      // Fallback to startup screen on error
      this.resetToScreen('Startup');
    }
  }

  /**
   * Navigate back from email verification to email input
   */
  navigateBackFromEmailVerification() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_BACK_FROM_EMAIL_VERIFICATION');
    nav.goBack();
  }

  // ==================== PHONE FLOW NAVIGATION ====================

  /**
   * Navigate from phone input to phone verification
   */
  navigateToPhoneVerification(phoneNumber: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_PHONE_VERIFICATION', { phoneNumber: '***REDACTED***' });
    nav.navigate('Verification', { phoneNumber });
  }

  /**
   * Navigate back from phone verification to phone input
   */
  navigateBackFromPhoneVerification() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_BACK_FROM_PHONE_VERIFICATION');
    nav.goBack();
  }

  /**
   * Handle successful phone verification and navigate to setup flow
   */
  navigateAfterPhoneVerification(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_AFTER_PHONE_VERIFICATION', { hasUserData: !!userData });

    // Determine next step based on user data
    if (!userData.firstName || userData.firstName.trim() === '') {
      nav.navigate('NameSetup', { userData });
    } else if (!userData.pin || userData.pin === '0000' || userData.pin === '') {
      nav.navigate('PinSetup', { userData });
    } else {
      nav.navigate('PinVerification', { user: userData });
    }
  }

  // ==================== SETUP FLOW NAVIGATION ====================

  /**
   * Navigate to name setup screen
   */
  navigateToNameSetup(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_NAME_SETUP');
    nav.navigate('NameSetup', { userData });
  }

  /**
   * Navigate from name setup to next step
   */
  navigateFromNameSetup(userData: any) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_NAME_SETUP');
    nav.navigate('PinSetup', { userData });
  }



  /**
   * Navigate from PIN verification to main app
   */
  navigateFromPinVerification() {
    this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_PIN_VERIFICATION');
    this.resetToScreen('MainTabs');
  }



  /**
   * Navigate from setup complete to main app
   */
  navigateFromSetupComplete() {
    this.ensureNavigation();
    logger.userAction('NAVIGATE_FROM_SETUP_COMPLETE');
    this.resetToScreen('MainTabs');
  }

  // ==================== MAIN APP NAVIGATION ====================
  
  /**
   * Navigate to profile screen
   */
  navigateToProfile(userId: string) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_PROFILE', { userId: '***REDACTED***' });
    nav.navigate('Profile', { userId });
  }

  /**
   * Navigate to settings screen
   */
  navigateToSettings() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SETTINGS');
    nav.navigate('Settings');
  }

  /**
   * Navigate to security screen
   */
  navigateToSecurity() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SECURITY');
    nav.navigate('Security');
  }

  /**
   * Navigate to appearance screen
   */
  navigateToAppearance() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_APPEARANCE');
    nav.navigate('Appearance');
  }

  /**
   * Navigate to refer & earn screen
   */
  navigateToReferEarn() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_REFER_EARN');
    nav.navigate('ReferEarn');
  }

  /**
   * Navigate to airtime screen
   */
  navigateToAirtime() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_AIRTIME');
    nav.navigate('Airtime');
  }

  /**
   * Navigate to setup loading screen with optional next destination
   */
  navigateToSetupLoading(nextScreen?: keyof RootStackParamList) {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_SETUP_LOADING', { nextScreen });
    nav.navigate('SetupLoading', nextScreen ? { next: nextScreen } : undefined);
  }



  /**
   * Navigate to history tab (for clock button in home screen)
   */
  navigateToHistoryTab() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_HISTORY_TAB');
    nav.navigate('HistoryTab' as never);
  }

  // ==================== UTILITY NAVIGATION ====================
  
  /**
   * Navigate back to previous screen
   */
  goBack() {
    const nav = this.ensureNavigation();
    logger.userAction('NAVIGATE_BACK');
    nav.goBack();
  }

  /**
   * Reset navigation stack to a specific screen
   */
  resetToScreen(screenName: keyof RootStackParamList, params?: any) {
    const nav = this.ensureNavigation();
    logger.userAction('RESET_TO_SCREEN', { screenName });
    nav.reset({
      index: 0,
      routes: [{ name: screenName, params }],
    });
  }

  /**
   * Navigate to startup screen (used for logout or errors)
   */
  navigateToStartup() {
    this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_STARTUP');
    this.resetToScreen('Startup');
  }

  /**
   * Navigate to main tabs (used after successful authentication)
   */
  navigateToMainTabs() {
    this.ensureNavigation();
    logger.userAction('NAVIGATE_TO_MAIN_TABS');
    this.resetToScreen('MainTabs');
  }

  // ==================== ERROR HANDLING ====================
  
  /**
   * Handle navigation errors by redirecting to startup
   */
  handleNavigationError(error: any, context?: string) {
    logger.error(`Navigation error occurred${context ? ` in ${context}` : ''}`, error, 'navigation');
    try {
      this.navigateToStartup();
    } catch (fallbackError) {
      logger.error('Failed to navigate to startup after error', fallbackError, 'navigation');
    }
  }

  /**
   * Handle user deletion by clearing data and redirecting to startup
   */
  handleUserDeletion() {
    logger.security('USER_DELETION_NAVIGATION', null);
    this.resetToScreen('Startup');
  }
}

// Export singleton instance
export const navigationHandler = new NavigationHandler();
export default navigationHandler;