# Email Verification Debug Guide

## Current Issue
User is stuck on the email verification screen after successful OTP verification. The backend logs show successful verification and token generation, but the frontend is not navigating to the next step.

## Debugging Improvements Added

### 1. Enhanced Response Logging
Added detailed logging to track the response structure:
- Backend status vs HTTP status
- Token location in response
- Response structure analysis

### 2. Fixed Response Structure Handling
The backend returns:
```json
{
  "status": "success",
  "message": "<PERSON><PERSON> verified successfully",
  "data": {
    "user": {...},
    "tokens": {...}
  }
}
```

Frontend receives:
```javascript
{
  data: { /* backend response */ },
  status: 200 /* HTTP status */
}
```

Fixed token access: `response.data.data.tokens` instead of `response.data.tokens`

### 3. Added Debug Methods
Global debug methods available in development:
```javascript
// Test navigation without verification
global.debugEmailVerification.testNavigation()

// Test token storage and retrieval
global.debugEmailVerification.testTokens()

// Get current email
global.debugEmailVerification.email
```

### 4. Enhanced Error Handling
- Better error messages with full response logging
- Graceful fallback for unexpected response structures
- Detailed token storage verification

## Debugging Steps

### Step 1: Check Console Logs
Look for these log entries after OTP verification:
- ✅ `"Email verification successful - navigating to next step"`
- ✅ `"Token storage verified successfully"`
- ✅ `"Starting navigation after email verification"`

### Step 2: Test Token Storage
In React Native debugger console:
```javascript
global.debugEmailVerification.testTokens()
```

Expected output:
- `hasAccessToken: true`
- `hasRefreshToken: true`
- `accessTokenPreview: "eyJhbGciOiJIUzI1NiIs..."`

### Step 3: Test Navigation
In React Native debugger console:
```javascript
global.debugEmailVerification.testNavigation()
```

This will attempt navigation without verification to isolate the issue.

### Step 4: Check Setup Service
The navigation calls `setupService.getSetupStatus()` which might be failing. Look for:
- ✅ `"Setup status retrieved successfully"`
- ❌ `"Auth failed for setup status"`

## Potential Issues

### 1. Token Storage Race Condition
- API service stores tokens automatically
- EmailVerificationScreen also tries to verify storage
- Could cause timing issues

### 2. Response Structure Mismatch
- Backend response structure might be different than expected
- Token location might vary

### 3. Navigation Handler Issues
- Setup service might be failing to get user status
- Authentication might be failing with new tokens

### 4. Cache Issues
- Despite the fix, cache might still be serving old tokens
- Token verification might be failing

## Next Steps

### If Token Storage Fails
1. Check if tokens are in the response
2. Verify token storage method is working
3. Check for keychain access issues

### If Navigation Fails
1. Check setup service logs
2. Verify authentication with new tokens
3. Check navigation handler logic

### If Everything Looks Good
1. Add breakpoints in navigation handler
2. Check if navigation is actually being called
3. Verify React Navigation state

## Quick Fix Test

Try this in the console while stuck on verification screen:
```javascript
// Force navigation test
global.debugEmailVerification.testNavigation()

// If that works, the issue is in the verification flow
// If that fails, the issue is in the navigation logic
```

## Files Modified for Debugging

1. `src/features/authentication/screens/EmailVerificationScreen.tsx`
   - Enhanced response handling
   - Added debug methods
   - Better error logging

2. `src/infrastructure/api/apiService.ts`
   - Enhanced token storage logging
   - Fixed token location detection

3. `src/navigation/handlers/navigationHandler.ts`
   - Added navigation process logging

## Expected Behavior After Fix

1. **OTP verification succeeds** ✅ (confirmed in logs)
2. **Tokens are stored** ✅ (should be confirmed with debug)
3. **Navigation starts** ❓ (needs verification)
4. **Setup status retrieved** ❓ (needs verification)
5. **User routed to next step** ❓ (final goal)

The enhanced logging will help identify exactly where the flow is breaking down.
